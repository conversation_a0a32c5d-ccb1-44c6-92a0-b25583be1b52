import html2canvas from 'html2canvas';
import jsPD<PERSON> from 'jspdf';
import { CertificateFormData } from '../page';

export async function generatePDF(
  element: HTMLElement, 
  templateName: string, 
  formData: CertificateFormData
): Promise<void> {
  try {
    // A4 portrait dimensions in pixels (at 96 DPI)
    const A4_PORTRAIT_WIDTH = 794;   // 8.27 inches * 96 DPI
    const A4_PORTRAIT_HEIGHT = 1123; // 11.69 inches * 96 DPI

    // Create a clone of the element to avoid visual disruption
    const clonedElement = element.cloneNode(true) as HTMLElement;

    // Set up the clone with proper styling for capture
    clonedElement.style.transform = 'scale(1)';
    clonedElement.style.transformOrigin = 'top left';
    clonedElement.style.position = 'absolute';
    clonedElement.style.top = '-9999px'; // Move off-screen
    clonedElement.style.left = '-9999px';
    clonedElement.style.width = `${A4_PORTRAIT_WIDTH}px`;
    clonedElement.style.height = `${A4_PORTRAIT_HEIGHT}px`;
    clonedElement.style.backgroundColor = '#ffffff';

    // Add clone to document temporarily
    document.body.appendChild(clonedElement);

    // Configure html2canvas options for better quality
    const canvas = await html2canvas(clonedElement, {
      scale: 2, // Higher scale for better quality
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: A4_PORTRAIT_WIDTH,
      height: A4_PORTRAIT_HEIGHT,
      scrollX: 0,
      scrollY: 0,
    });

    // Remove the clone from document
    document.body.removeChild(clonedElement);

    // Create PDF in portrait A4 format
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    // Get A4 portrait dimensions in mm
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    // Convert canvas to image and add to PDF
    const imgData = canvas.toDataURL('image/png');
    pdf.addImage(imgData, 'PNG', 0, 0, pageWidth, pageHeight);

    // Generate filename based on template name and form data
    const filename = generateFilename(templateName, formData);

    // Save the PDF
    pdf.save(filename);
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error('Failed to generate PDF');
  }
}

/**
 * Generate a filename for the PDF based on template name and form data
 */
function generateFilename(templateName: string, formData: CertificateFormData): string {
  // Try to find a name field in the form data
  const nameFields = ['name', 'fullName', 'firstName', 'lastName'];
  let personName = '';

  for (const field of nameFields) {
    if (formData[field]) {
      personName = formData[field];
      break;
    }
  }

  // If no name found, try combining firstName and lastName
  if (!personName && formData['firstName'] && formData['lastName']) {
    personName = `${formData['firstName']}_${formData['lastName']}`;
  }

  // Clean template name
  const cleanTemplateName = templateName.replace(/[^a-zA-Z0-9]/g, '_');

  // Generate filename
  if (personName) {
    const cleanPersonName = personName.replace(/[^a-zA-Z0-9]/g, '_');
    return `${cleanTemplateName}(${cleanPersonName}).pdf`;
  } else {
    const timestamp = new Date().toISOString().slice(0, 10);
    return `${cleanTemplateName}_${timestamp}.pdf`;
  }
}

/**
 * Alternative method using File constructor for better browser compatibility
 */
export async function generatePDFWithFileConstructor(
  element: HTMLElement, 
  templateName: string, 
  formData: CertificateFormData
): Promise<void> {
  try {
    // A4 portrait dimensions in pixels (at 96 DPI)
    const A4_PORTRAIT_WIDTH = 794;   // 8.27 inches * 96 DPI
    const A4_PORTRAIT_HEIGHT = 1123; // 11.69 inches * 96 DPI

    // Create a clone of the element to avoid visual disruption
    const clonedElement = element.cloneNode(true) as HTMLElement;

    // Set up the clone with proper styling for capture
    clonedElement.style.transform = 'scale(1)';
    clonedElement.style.transformOrigin = 'top left';
    clonedElement.style.position = 'absolute';
    clonedElement.style.top = '-9999px';
    clonedElement.style.left = '-9999px';
    clonedElement.style.width = `${A4_PORTRAIT_WIDTH}px`;
    clonedElement.style.height = `${A4_PORTRAIT_HEIGHT}px`;
    clonedElement.style.backgroundColor = '#ffffff';

    // Add clone to document temporarily
    document.body.appendChild(clonedElement);

    const canvas = await html2canvas(clonedElement, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: A4_PORTRAIT_WIDTH,
      height: A4_PORTRAIT_HEIGHT,
    });

    // Remove the clone from document
    document.body.removeChild(clonedElement);

    // Create PDF in portrait A4 format
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    // Get A4 portrait dimensions in mm
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    const imgData = canvas.toDataURL('image/png');
    pdf.addImage(imgData, 'PNG', 0, 0, pageWidth, pageHeight);

    // Get PDF as blob
    const pdfBlob = pdf.output('blob');
    
    // Create filename
    const filename = generateFilename(templateName, formData);

    // Create file and trigger download
    const file = new File([pdfBlob], filename, { type: 'application/pdf' });
    const url = URL.createObjectURL(file);
    
    // Create temporary link and trigger download
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    
    // Cleanup
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error('Failed to generate PDF');
  }
}
