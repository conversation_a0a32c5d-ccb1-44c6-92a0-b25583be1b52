"use client";

import { useState, useEffect } from "react";
import {
  TemplateUpload,
  TemplateManager,
  CertificateGenerator,
} from "./components";
import { saveTemplates, loadTemplates } from "./utils/templateStorage";

export interface TemplateData {
  id: string;
  name: string;
  file: File;
  content: string;
  placeholders: string[];
  uploadDate: Date;
  preview?: string;
}

export interface CertificateFormData {
  [key: string]: string;
}

export default function CertificateUploadPage() {
  const [templates, setTemplates] = useState<TemplateData[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<TemplateData | null>(
    null
  );
  const [currentView, setCurrentView] = useState<
    "upload" | "manage" | "generate"
  >("upload");
  const [formData, setFormData] = useState<CertificateFormData>({});

  // Load templates from localStorage on component mount
  useEffect(() => {
    const savedTemplates = loadTemplates();
    setTemplates(savedTemplates);
  }, []);

  // Save templates to localStorage whenever templates change
  useEffect(() => {
    if (templates.length > 0) {
      saveTemplates(templates);
    }
  }, [templates]);

  const handleTemplateUpload = (template: TemplateData) => {
    setTemplates((prev) => [...prev, template]);
  };

  const handleTemplateSelect = (template: TemplateData) => {
    setSelectedTemplate(template);
    setCurrentView("generate");
    // Initialize form data with empty values for all placeholders
    const initialFormData: CertificateFormData = {};
    template.placeholders.forEach((placeholder) => {
      initialFormData[placeholder] = "";
    });
    setFormData(initialFormData);
  };

  const handleTemplateDelete = (templateId: string) => {
    setTemplates((prev) => prev.filter((t) => t.id !== templateId));
    if (selectedTemplate?.id === templateId) {
      setSelectedTemplate(null);
      setCurrentView("upload");
    }
  };

  const handleBackToUpload = () => {
    setCurrentView("upload");
    setSelectedTemplate(null);
  };

  const handleBackToManage = () => {
    setCurrentView("manage");
    setSelectedTemplate(null);
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case "upload":
        return (
          <TemplateUpload
            onUpload={handleTemplateUpload}
            onViewTemplates={() => setCurrentView("manage")}
            hasTemplates={templates.length > 0}
          />
        );
      case "manage":
        return (
          <TemplateManager
            templates={templates}
            onSelectTemplate={handleTemplateSelect}
            onDeleteTemplate={handleTemplateDelete}
            onBack={handleBackToUpload}
          />
        );
      case "generate":
        return selectedTemplate ? (
          <CertificateGenerator
            template={selectedTemplate}
            formData={formData}
            onFormDataChange={setFormData}
            onBack={handleBackToManage}
          />
        ) : null;
      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto p-4 max-w-6xl">{renderCurrentView()}</div>
  );
}
