"use client";

import { useState, useRef } from "react";
import {
  <PERSON><PERSON>eft,
  FileText,
  Trash2,
  Eye,
  Calendar,
  Hash,
  Plus,
  Download,
  Upload,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { TemplateData } from "../page";
import {
  exportTemplates,
  importTemplatesFromFile,
} from "../utils/templateStorage";

interface TemplateManagerProps {
  templates: TemplateData[];
  onSelectTemplate: (template: TemplateData) => void;
  onDeleteTemplate: (templateId: string) => void;
  onBack: () => void;
}

export function TemplateManager({
  templates,
  onSelectTemplate,
  onDeleteTemplate,
  onBack,
}: TemplateManagerProps) {
  const [selectedTemplateId, setSelectedTemplateId] = useState<string | null>(
    null
  );
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDeleteTemplate = (templateId: string) => {
    onDeleteTemplate(templateId);
    toast.success("Template deleted successfully");
  };

  const handleExportTemplates = () => {
    try {
      exportTemplates(templates);
      toast.success("Templates exported successfully");
    } catch (error) {
      toast.error("Failed to export templates");
    }
  };

  const handleImportClick = () => {
    fileInputRef.current?.click();
  };

  const handleImportFile = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      const importedTemplates = await importTemplatesFromFile(file);
      // Note: This would need to be handled by the parent component
      // For now, just show a message
      toast.success(`Found ${importedTemplates.length} templates to import`);
      toast.info(
        "Import functionality needs to be implemented in parent component"
      );
    } catch (error) {
      toast.error("Failed to import templates");
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-semibold text-foreground">
            Template Manager
          </h1>
          <p className="text-muted-foreground text-lg">
            Manage your uploaded certificate templates
          </p>
        </div>
        <Button
          variant="outline"
          onClick={onBack}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Upload
        </Button>
      </div>

      {/* Templates Grid */}
      {templates.length === 0 ? (
        <Card className="text-center py-12">
          <CardContent>
            <FileText className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No Templates Uploaded</h3>
            <p className="text-muted-foreground mb-4">
              Upload your first certificate template to get started
            </p>
            <Button onClick={onBack} className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Upload Template
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {templates.map((template) => (
            <Card
              key={template.id}
              className={`transition-all duration-200 hover:shadow-md ${
                selectedTemplateId === template.id ? "ring-2 ring-primary" : ""
              }`}
            >
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <FileText className="w-5 h-5 text-primary" />
                  {template.name}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Template Info */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Calendar className="w-4 h-4" />
                    {formatDate(template.uploadDate)}
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Hash className="w-4 h-4" />
                    {template.placeholders.length} placeholders
                  </div>
                </div>

                {/* Placeholders */}
                <div className="space-y-2">
                  <p className="text-sm font-medium">Placeholders:</p>
                  <div className="flex flex-wrap gap-1">
                    {template.placeholders
                      .slice(0, 3)
                      .map((placeholder, index) => (
                        <Badge
                          key={index}
                          variant="secondary"
                          className="text-xs"
                        >
                          {"{" + placeholder + "}"}
                        </Badge>
                      ))}
                    {template.placeholders.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{template.placeholders.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Preview */}
                {template.preview && (
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Preview:</p>
                    <div className="bg-muted/30 rounded p-2 text-xs text-muted-foreground max-h-20 overflow-hidden">
                      {template.preview}
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex gap-2 pt-2">
                  <Button
                    onClick={() => onSelectTemplate(template)}
                    className="flex-1 flex items-center gap-2"
                    size="sm"
                  >
                    <Eye className="w-4 h-4" />
                    Generate
                  </Button>

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete Template</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete "{template.name}"?
                          This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => handleDeleteTemplate(template.id)}
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Summary */}
      {templates.length > 0 && (
        <Card className="bg-muted/30">
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
              <div>
                <p className="font-medium">
                  Total Templates: {templates.length}
                </p>
                <p className="text-sm text-muted-foreground">
                  Click "Generate" on any template to create certificates
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-2">
                <Button
                  onClick={handleExportTemplates}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Download className="w-4 h-4" />
                  Export Templates
                </Button>
                <Button
                  onClick={handleImportClick}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Upload className="w-4 h-4" />
                  Import Templates
                </Button>
                <Button
                  onClick={onBack}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Plus className="w-4 h-4" />
                  Upload New Template
                </Button>
              </div>
            </div>

            {/* Hidden file input for import */}
            <input
              ref={fileInputRef}
              type="file"
              accept=".json"
              onChange={handleImportFile}
              className="hidden"
            />
          </CardContent>
        </Card>
      )}
    </div>
  );
}
