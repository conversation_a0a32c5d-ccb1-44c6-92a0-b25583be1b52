import { TemplateData } from '../page';

const STORAGE_KEY = 'certificate_templates';

/**
 * Save templates to localStorage
 */
export function saveTemplates(templates: TemplateData[]): void {
  try {
    // Convert templates to a serializable format (without File objects)
    const serializableTemplates = templates.map(template => ({
      ...template,
      file: {
        name: template.file.name,
        size: template.file.size,
        type: template.file.type,
        lastModified: template.file.lastModified,
      },
      uploadDate: template.uploadDate.toISOString(),
    }));

    localStorage.setItem(STORAGE_KEY, JSON.stringify(serializableTemplates));
  } catch (error) {
    console.error('Failed to save templates to localStorage:', error);
  }
}

/**
 * Load templates from localStorage
 */
export function loadTemplates(): TemplateData[] {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) return [];

    const parsed = JSON.parse(stored);
    
    // Convert back to TemplateData format
    return parsed.map((template: any) => ({
      ...template,
      uploadDate: new Date(template.uploadDate),
      // Note: File object cannot be restored from localStorage
      // This is a limitation - files would need to be re-uploaded
      file: new File([''], template.file.name, {
        type: template.file.type,
        lastModified: template.file.lastModified,
      }),
    }));
  } catch (error) {
    console.error('Failed to load templates from localStorage:', error);
    return [];
  }
}

/**
 * Clear all templates from localStorage
 */
export function clearTemplates(): void {
  try {
    localStorage.removeItem(STORAGE_KEY);
  } catch (error) {
    console.error('Failed to clear templates from localStorage:', error);
  }
}

/**
 * Export templates as JSON file
 */
export function exportTemplates(templates: TemplateData[]): void {
  try {
    const exportData = templates.map(template => ({
      id: template.id,
      name: template.name,
      content: template.content,
      placeholders: template.placeholders,
      uploadDate: template.uploadDate.toISOString(),
      preview: template.preview,
      fileName: template.file.name,
      fileType: template.file.type,
    }));

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `certificate_templates_${new Date().toISOString().slice(0, 10)}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Failed to export templates:', error);
    throw new Error('Failed to export templates');
  }
}

/**
 * Import templates from JSON file
 */
export function importTemplatesFromFile(file: File): Promise<Partial<TemplateData>[]> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const importedData = JSON.parse(content);
        
        if (!Array.isArray(importedData)) {
          throw new Error('Invalid file format');
        }

        const templates = importedData.map((item: any) => ({
          id: item.id,
          name: item.name,
          content: item.content,
          placeholders: item.placeholders,
          uploadDate: new Date(item.uploadDate),
          preview: item.preview,
          // File object needs to be recreated or re-uploaded
          file: new File([''], item.fileName || 'imported_template.txt', {
            type: item.fileType || 'text/plain',
          }),
        }));

        resolve(templates);
      } catch (error) {
        reject(new Error('Failed to parse template file'));
      }
    };

    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };

    reader.readAsText(file);
  });
}

/**
 * Get template statistics
 */
export function getTemplateStats(templates: TemplateData[]) {
  const totalTemplates = templates.length;
  const totalPlaceholders = templates.reduce((sum, template) => sum + template.placeholders.length, 0);
  const averagePlaceholders = totalTemplates > 0 ? Math.round(totalPlaceholders / totalTemplates) : 0;
  
  const placeholderFrequency: Record<string, number> = {};
  templates.forEach(template => {
    template.placeholders.forEach(placeholder => {
      placeholderFrequency[placeholder] = (placeholderFrequency[placeholder] || 0) + 1;
    });
  });

  const mostCommonPlaceholders = Object.entries(placeholderFrequency)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5)
    .map(([placeholder, count]) => ({ placeholder, count }));

  return {
    totalTemplates,
    totalPlaceholders,
    averagePlaceholders,
    mostCommonPlaceholders,
  };
}
