import { TemplateData } from '../page';

/**
 * Extract placeholders from template content
 * Supports formats like {name}, {%image}, etc.
 */
export function extractPlaceholders(content: string): string[] {
  const placeholderRegex = /\{([^}]+)\}/g;
  const placeholders: string[] = [];
  let match;

  while ((match = placeholderRegex.exec(content)) !== null) {
    const placeholder = match[1];
    if (!placeholders.includes(placeholder)) {
      placeholders.push(placeholder);
    }
  }

  return placeholders;
}

/**
 * Replace placeholders in template content with actual values
 */
export function replacePlaceholders(content: string, data: Record<string, string>): string {
  let processedContent = content;

  Object.entries(data).forEach(([key, value]) => {
    const placeholder = `{${key}}`;
    processedContent = processedContent.replace(new RegExp(placeholder, 'g'), value || '');
  });

  return processedContent;
}

/**
 * Validate template file
 */
export function validateTemplateFile(file: File): { isValid: boolean; error?: string } {
  const maxSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = [
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
    'application/msword', // .doc
    'text/html', // .html
    'text/plain' // .txt
  ];

  if (file.size > maxSize) {
    return { isValid: false, error: 'File size must be less than 10MB' };
  }

  if (!allowedTypes.includes(file.type)) {
    return { isValid: false, error: 'Only .docx, .doc, .html, and .txt files are supported' };
  }

  return { isValid: true };
}

/**
 * Generate unique ID for template
 */
export function generateTemplateId(): string {
  return `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Read file content as text
 */
export function readFileAsText(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      resolve(result);
    };
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsText(file);
  });
}

/**
 * Create template preview (first 200 characters)
 */
export function createTemplatePreview(content: string): string {
  return content.length > 200 ? content.substring(0, 200) + '...' : content;
}
