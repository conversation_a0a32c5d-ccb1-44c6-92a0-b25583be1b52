"use client";

import { useState, useRef } from "react";
import { Upload, FileText, Eye, AlertCircle, CheckCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { TemplateData } from "../page";
import { 
  validateTemplateFile, 
  readFileAsText, 
  extractPlaceholders, 
  generateTemplateId,
  createTemplatePreview 
} from "../utils/templateProcessor";
import { cn } from "@/lib/utils";

interface TemplateUploadProps {
  onUpload: (template: TemplateData) => void;
  onViewTemplates: () => void;
  hasTemplates: boolean;
}

export function TemplateUpload({ onUpload, onViewTemplates, hasTemplates }: TemplateUploadProps) {
  const [templateName, setTemplateName] = useState("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [fileContent, setFileContent] = useState("");
  const [placeholders, setPlaceholders] = useState<string[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (file: File) => {
    const validation = validateTemplateFile(file);
    if (!validation.isValid) {
      toast.error(validation.error);
      return;
    }

    setIsProcessing(true);
    try {
      const content = await readFileAsText(file);
      const extractedPlaceholders = extractPlaceholders(content);
      
      setSelectedFile(file);
      setFileContent(content);
      setPlaceholders(extractedPlaceholders);
      setTemplateName(file.name.replace(/\.[^/.]+$/, "")); // Remove file extension
      
      toast.success("Template file processed successfully!");
    } catch (error) {
      toast.error("Failed to process template file");
      console.error(error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const file = e.dataTransfer.files[0];
    if (file) handleFileSelect(file);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) handleFileSelect(file);
  };

  const handleUpload = () => {
    if (!selectedFile || !templateName.trim()) {
      toast.error("Please provide a template name and select a file");
      return;
    }

    const template: TemplateData = {
      id: generateTemplateId(),
      name: templateName.trim(),
      file: selectedFile,
      content: fileContent,
      placeholders,
      uploadDate: new Date(),
      preview: createTemplatePreview(fileContent)
    };

    onUpload(template);
    
    // Reset form
    setTemplateName("");
    setSelectedFile(null);
    setFileContent("");
    setPlaceholders([]);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }

    toast.success("Template uploaded successfully!");
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-semibold text-foreground">Upload Certificate Template</h1>
        <p className="text-muted-foreground text-lg">
          Upload a certificate template with placeholders like {"{name}"}, {"{date}"}, etc.
        </p>
      </div>

      {/* Upload Form */}
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="w-5 h-5" />
            Template Upload
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Template Name */}
          <div className="space-y-2">
            <Label htmlFor="templateName">Template Name</Label>
            <Input
              id="templateName"
              value={templateName}
              onChange={(e) => setTemplateName(e.target.value)}
              placeholder="Enter template name (e.g., Good Moral Certificate)"
              className="bg-input/30 border-border"
            />
          </div>

          {/* File Upload Area */}
          <div
            className={cn(
              "border-2 border-dashed rounded-lg p-8 text-center transition-colors",
              isDragOver ? "border-primary bg-primary/5" : "border-border",
              selectedFile ? "border-green-500 bg-green-50 dark:bg-green-950/20" : ""
            )}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
          >
            {selectedFile ? (
              <div className="space-y-3">
                <CheckCircle className="w-12 h-12 text-green-500 mx-auto" />
                <div>
                  <p className="font-medium text-green-700 dark:text-green-400">
                    {selectedFile.name}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {(selectedFile.size / 1024).toFixed(1)} KB
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <FileText className="w-12 h-12 text-muted-foreground mx-auto" />
                <div>
                  <p className="font-medium">Drop your template file here</p>
                  <p className="text-sm text-muted-foreground">
                    or click to browse (.docx, .doc, .html, .txt)
                  </p>
                </div>
              </div>
            )}
            
            <input
              ref={fileInputRef}
              type="file"
              accept=".docx,.doc,.html,.txt"
              onChange={handleFileInputChange}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            />
          </div>

          {/* Placeholders Preview */}
          {placeholders.length > 0 && (
            <div className="space-y-3">
              <Label>Detected Placeholders</Label>
              <div className="flex flex-wrap gap-2">
                {placeholders.map((placeholder, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium"
                  >
                    {"{" + placeholder + "}"}
                  </span>
                ))}
              </div>
              <p className="text-sm text-muted-foreground">
                These placeholders will be replaced with actual data when generating certificates.
              </p>
            </div>
          )}

          {/* Content Preview */}
          {fileContent && (
            <div className="space-y-3">
              <Label>Template Preview</Label>
              <div className="bg-muted/30 rounded-lg p-4 max-h-40 overflow-y-auto">
                <pre className="text-sm whitespace-pre-wrap text-muted-foreground">
                  {createTemplatePreview(fileContent)}
                </pre>
              </div>
            </div>
          )}

          {/* Upload Button */}
          <Button
            onClick={handleUpload}
            disabled={!selectedFile || !templateName.trim() || isProcessing}
            className="w-full"
          >
            {isProcessing ? "Processing..." : "Upload Template"}
          </Button>
        </CardContent>
      </Card>

      {/* View Templates Button */}
      {hasTemplates && (
        <div className="text-center">
          <Button variant="outline" onClick={onViewTemplates} className="flex items-center gap-2">
            <Eye className="w-4 h-4" />
            View Uploaded Templates
          </Button>
        </div>
      )}
    </div>
  );
}
