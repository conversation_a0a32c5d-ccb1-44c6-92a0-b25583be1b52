"use client";

import { useState, useRef, useEffect } from "react";
import { ArrowLeft, Download, Edit, Eye, User, FileText } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { TemplateData, CertificateFormData } from "../page";
import { replacePlaceholders } from "../utils/templateProcessor";
import { generatePDF } from "../utils/pdfGenerator";

interface CertificateGeneratorProps {
  template: TemplateData;
  formData: CertificateFormData;
  onFormDataChange: (data: CertificateFormData) => void;
  onBack: () => void;
}

export function CertificateGenerator({
  template,
  formData,
  onFormDataChange,
  onBack,
}: CertificateGeneratorProps) {
  const [showPreview, setShowPreview] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [scale, setScale] = useState(0.7);
  const certificateRef = useRef<HTMLDivElement>(null);

  // Calculate scale for preview
  useEffect(() => {
    const calculateScale = () => {
      if (certificateRef.current) {
        const container = certificateRef.current.parentElement;
        if (container) {
          const containerWidth = container.clientWidth;
          const certificateWidth = 794; // A4 width in pixels
          const newScale = Math.min(containerWidth / certificateWidth, 0.8);
          setScale(newScale);
        }
      }
    };

    calculateScale();
    window.addEventListener("resize", calculateScale);
    return () => window.removeEventListener("resize", calculateScale);
  }, [showPreview]);

  const handleInputChange = (field: string, value: string) => {
    onFormDataChange({ ...formData, [field]: value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate processing
    await new Promise((resolve) => setTimeout(resolve, 1000));

    setIsSubmitting(false);
    setShowPreview(true);
    toast.success("Certificate generated successfully!");
  };

  const handleDownloadPDF = async () => {
    if (certificateRef.current) {
      try {
        await generatePDF(certificateRef.current, template.name, formData);
      } catch (error) {
        console.error("Error generating PDF:", error);
        toast.error("Error generating PDF. Please try again.");
      }
    }
  };

  const getProcessedContent = () => {
    return replacePlaceholders(template.content, formData);
  };

  const isFormValid = () => {
    return template.placeholders.every(placeholder => 
      formData[placeholder] && formData[placeholder].trim() !== ''
    );
  };

  if (showPreview) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-semibold text-foreground">Certificate Preview</h1>
            <p className="text-muted-foreground text-lg">
              Preview and download your generated certificate
            </p>
          </div>
          <Button variant="outline" onClick={onBack} className="flex items-center gap-2">
            <ArrowLeft className="w-4 h-4" />
            Back to Templates
          </Button>
        </div>

        {/* Certificate Preview */}
        <Card className="overflow-hidden">
          <CardContent className="p-0">
            <div className="flex justify-center bg-muted/20 p-8">
              <div
                ref={certificateRef}
                className="bg-white shadow-lg"
                style={{
                  width: "794px",
                  minHeight: "1123px",
                  transform: `scale(${scale})`,
                  transformOrigin: "top center",
                }}
              >
                <div className="p-12 h-full">
                  <pre className="whitespace-pre-wrap font-serif text-base leading-relaxed">
                    {getProcessedContent()}
                  </pre>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
          <Button
            variant="outline"
            onClick={() => setShowPreview(false)}
            className="flex items-center gap-2 w-full sm:w-auto"
          >
            <Edit className="w-4 h-4" />
            Edit Form
          </Button>
          <Button
            onClick={handleDownloadPDF}
            className="flex items-center gap-2 w-full sm:w-auto"
          >
            <Download className="w-4 h-4" />
            Download PDF
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-semibold text-foreground">Generate Certificate</h1>
          <p className="text-muted-foreground text-lg">
            Fill in the details for: <span className="font-medium">{template.name}</span>
          </p>
        </div>
        <Button variant="outline" onClick={onBack} className="flex items-center gap-2">
          <ArrowLeft className="w-4 h-4" />
          Back to Templates
        </Button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Form Fields */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="w-5 h-5" />
              Certificate Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {template.placeholders.map((placeholder) => (
                <div key={placeholder} className="space-y-2">
                  <Label htmlFor={placeholder} className="text-card-foreground">
                    {placeholder.charAt(0).toUpperCase() + placeholder.slice(1).replace(/([A-Z])/g, ' $1')}
                  </Label>
                  <Input
                    id={placeholder}
                    value={formData[placeholder] || ''}
                    onChange={(e) => handleInputChange(placeholder, e.target.value)}
                    className="bg-input/30 border-border text-foreground placeholder:text-muted-foreground focus:border-primary"
                    placeholder={`Enter ${placeholder}`}
                    required
                  />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Template Preview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5" />
              Template Preview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-muted/30 rounded-lg p-4 max-h-60 overflow-y-auto">
              <pre className="text-sm whitespace-pre-wrap text-muted-foreground">
                {template.content}
              </pre>
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              Placeholders will be replaced with your input values
            </p>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-center">
          <Button
            type="submit"
            disabled={!isFormValid() || isSubmitting}
            className="w-full sm:w-auto px-8"
          >
            {isSubmitting ? "Generating..." : "Generate Certificate"}
          </Button>
        </div>
      </form>
    </div>
  );
}
